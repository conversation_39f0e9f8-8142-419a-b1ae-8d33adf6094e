<?php
/**
 * Custom Homepage Template for Rooftops Barcelona
 */

get_header(); ?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-content">
        <h1>Discover Barcelona's Best Rooftops</h1>
        <p>Explore stunning rooftop bars, restaurants, and terraces with breathtaking views of the beautiful city of Barcelona</p>
        <a href="#featured-rooftops" class="hero-btn">Explore Rooftops</a>
    </div>
</section>

<!-- Most Popular Rooftops Features Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Most Popular Rooftops Features in Barcelona</h2>
        <p class="section-subtitle">Discover rooftops by their most sought-after features and experiences</p>
        
        <div class="cards-grid">
            <?php
            $popular_features = get_terms(array(
                'taxonomy' => 'popular_features',
                'hide_empty' => false,
                'number' => 6
            ));
            
            $feature_images = array(
                'couples' => 'https://images.unsplash.com/photo-1516997121675-4c2d1684aa3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'groups' => 'https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'romantic' => 'https://images.unsplash.com/photo-1519671482749-fd09be7ccebf?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'sunset-views' => 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'live-music' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'business' => 'https://images.unsplash.com/photo-1556761175-4b46a572b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
            );
            
            // If no terms exist, create sample data
            if (empty($popular_features)) {
                $sample_features = array(
                    array('name' => 'Couples', 'slug' => 'couples', 'description' => 'Perfect intimate settings for romantic evenings'),
                    array('name' => 'Groups', 'slug' => 'groups', 'description' => 'Spacious areas ideal for group celebrations'),
                    array('name' => 'Romantic', 'slug' => 'romantic', 'description' => 'Enchanting atmospheres for special moments'),
                    array('name' => 'Sunset Views', 'slug' => 'sunset-views', 'description' => 'Spectacular sunset viewing experiences'),
                    array('name' => 'Live Music', 'slug' => 'live-music', 'description' => 'Venues featuring live musical performances'),
                    array('name' => 'Business', 'slug' => 'business', 'description' => 'Professional settings for business meetings')
                );
                
                foreach ($sample_features as $feature) {
                    $image_url = isset($feature_images[$feature['slug']]) ? $feature_images[$feature['slug']] : $feature_images['couples'];
                    echo '<div class="card" onclick="window.location.href=\'/popular/' . $feature['slug'] . '/\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $feature['name'] . '</h3>';
                    echo '<p class="card-description">' . $feature['description'] . '</p>';
                    echo '<a href="/popular/' . $feature['slug'] . '/" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            } else {
                foreach ($popular_features as $feature) {
                    $image_url = isset($feature_images[$feature->slug]) ? $feature_images[$feature->slug] : $feature_images['couples'];
                    echo '<div class="card" onclick="window.location.href=\'' . get_term_link($feature) . '\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $feature->name . '</h3>';
                    echo '<p class="card-description">' . $feature->description . '</p>';
                    echo '<a href="' . get_term_link($feature) . '" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            }
            ?>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Explore Rooftops Services in Barcelona</h2>
        <p class="section-subtitle">Find rooftops offering the services you're looking for</p>
        
        <div class="cards-grid">
            <?php
            $services = get_terms(array(
                'taxonomy' => 'services',
                'hide_empty' => false,
                'number' => 6
            ));
            
            $service_images = array(
                'dining' => 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'cocktails' => 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'events' => 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'brunch' => 'https://images.unsplash.com/photo-1482049016688-2d3e1b311543?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'dj-sets' => 'https://images.unsplash.com/photo-1571266028243-d220c9c3b31f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'wine-tasting' => 'https://images.unsplash.com/photo-1506377247377-2a5b3b417ebb?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
            );
            
            // If no terms exist, create sample data
            if (empty($services)) {
                $sample_services = array(
                    array('name' => 'Fine Dining', 'slug' => 'dining', 'description' => 'Exquisite culinary experiences with city views'),
                    array('name' => 'Cocktails', 'slug' => 'cocktails', 'description' => 'Craft cocktails and premium spirits'),
                    array('name' => 'Private Events', 'slug' => 'events', 'description' => 'Exclusive venues for special occasions'),
                    array('name' => 'Brunch', 'slug' => 'brunch', 'description' => 'Weekend brunch with panoramic views'),
                    array('name' => 'DJ Sets', 'slug' => 'dj-sets', 'description' => 'Live DJ performances and dancing'),
                    array('name' => 'Wine Tasting', 'slug' => 'wine-tasting', 'description' => 'Curated wine selections and tastings')
                );
                
                foreach ($sample_services as $service) {
                    $image_url = isset($service_images[$service['slug']]) ? $service_images[$service['slug']] : $service_images['dining'];
                    echo '<div class="card" onclick="window.location.href=\'/services/' . $service['slug'] . '/\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $service['name'] . '</h3>';
                    echo '<p class="card-description">' . $service['description'] . '</p>';
                    echo '<a href="/services/' . $service['slug'] . '/" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            } else {
                foreach ($services as $service) {
                    $image_url = isset($service_images[$service->slug]) ? $service_images[$service->slug] : $service_images['dining'];
                    echo '<div class="card" onclick="window.location.href=\'' . get_term_link($service) . '\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $service->name . '</h3>';
                    echo '<p class="card-description">' . $service->description . '</p>';
                    echo '<a href="' . get_term_link($service) . '" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            }
            ?>
        </div>
    </div>
</section>

<!-- Amenities Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Find Barcelona Rooftops by Amenities</h2>
        <p class="section-subtitle">Discover rooftops with the amenities that matter to you</p>
        
        <div class="cards-grid">
            <?php
            $amenities = get_terms(array(
                'taxonomy' => 'amenities',
                'hide_empty' => false,
                'number' => 6
            ));
            
            $amenity_images = array(
                'pool' => 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'bar' => 'https://images.unsplash.com/photo-1572116469696-31de0f17cc34?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'terrace' => 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'garden' => 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'lounge' => 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'parking' => 'https://images.unsplash.com/photo-1506521781263-d8422e82f27a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
            );
            
            // If no terms exist, create sample data
            if (empty($amenities)) {
                $sample_amenities = array(
                    array('name' => 'Swimming Pool', 'slug' => 'pool', 'description' => 'Refreshing pools with stunning city views'),
                    array('name' => 'Full Bar', 'slug' => 'bar', 'description' => 'Complete bar service with premium drinks'),
                    array('name' => 'Open Terrace', 'slug' => 'terrace', 'description' => 'Spacious outdoor terraces for relaxation'),
                    array('name' => 'Garden Area', 'slug' => 'garden', 'description' => 'Beautiful garden spaces with greenery'),
                    array('name' => 'Lounge Seating', 'slug' => 'lounge', 'description' => 'Comfortable lounge areas for socializing'),
                    array('name' => 'Parking', 'slug' => 'parking', 'description' => 'Convenient parking facilities available')
                );
                
                foreach ($sample_amenities as $amenity) {
                    $image_url = isset($amenity_images[$amenity['slug']]) ? $amenity_images[$amenity['slug']] : $amenity_images['terrace'];
                    echo '<div class="card" onclick="window.location.href=\'/amenities/' . $amenity['slug'] . '/\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $amenity['name'] . '</h3>';
                    echo '<p class="card-description">' . $amenity['description'] . '</p>';
                    echo '<a href="/amenities/' . $amenity['slug'] . '/" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            } else {
                foreach ($amenities as $amenity) {
                    $image_url = isset($amenity_images[$amenity->slug]) ? $amenity_images[$amenity->slug] : $amenity_images['terrace'];
                    echo '<div class="card" onclick="window.location.href=\'' . get_term_link($amenity) . '\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $amenity->name . '</h3>';
                    echo '<p class="card-description">' . $amenity->description . '</p>';
                    echo '<a href="' . get_term_link($amenity) . '" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            }
            ?>
        </div>
    </div>
</section>

<!-- Neighborhoods Section -->
<section class="section" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title">Explore Rooftops by Barcelona Neighborhoods</h2>
        <p class="section-subtitle">Discover amazing rooftops in Barcelona's most vibrant districts</p>
        
        <div class="cards-grid">
            <?php
            $neighborhoods = get_terms(array(
                'taxonomy' => 'neighborhoods',
                'hide_empty' => false,
                'number' => 6
            ));
            
            $neighborhood_images = array(
                'eixample' => 'https://images.unsplash.com/photo-1539037116277-4db20889f2d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'gothic-quarter' => 'https://images.unsplash.com/photo-1511527661048-7fe73d85e9a4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'gracia' => 'https://images.unsplash.com/photo-1523531294919-4bcd7c65e216?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'born' => 'https://images.unsplash.com/photo-1558642452-9d2a7deb7f62?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'barceloneta' => 'https://images.unsplash.com/photo-1571501679680-de32f1e7aad4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                'poblenou' => 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
            );
            
            // If no terms exist, create sample data
            if (empty($neighborhoods)) {
                $sample_neighborhoods = array(
                    array('name' => 'Eixample', 'slug' => 'eixample', 'description' => 'Modern rooftops in the heart of Barcelona'),
                    array('name' => 'Gothic Quarter', 'slug' => 'gothic-quarter', 'description' => 'Historic charm meets contemporary style'),
                    array('name' => 'Gràcia', 'slug' => 'gracia', 'description' => 'Bohemian neighborhood with unique rooftops'),
                    array('name' => 'El Born', 'slug' => 'born', 'description' => 'Trendy area with artistic rooftop venues'),
                    array('name' => 'Barceloneta', 'slug' => 'barceloneta', 'description' => 'Beachside rooftops with sea views'),
                    array('name' => 'Poblenou', 'slug' => 'poblenou', 'description' => 'Tech district with innovative rooftop spaces')
                );
                
                foreach ($sample_neighborhoods as $neighborhood) {
                    $image_url = isset($neighborhood_images[$neighborhood['slug']]) ? $neighborhood_images[$neighborhood['slug']] : $neighborhood_images['eixample'];
                    echo '<div class="card" onclick="window.location.href=\'/neighborhoods/' . $neighborhood['slug'] . '/\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $neighborhood['name'] . '</h3>';
                    echo '<p class="card-description">' . $neighborhood['description'] . '</p>';
                    echo '<a href="/neighborhoods/' . $neighborhood['slug'] . '/" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            } else {
                foreach ($neighborhoods as $neighborhood) {
                    $image_url = isset($neighborhood_images[$neighborhood->slug]) ? $neighborhood_images[$neighborhood->slug] : $neighborhood_images['eixample'];
                    echo '<div class="card" onclick="window.location.href=\'' . get_term_link($neighborhood) . '\'">';
                    echo '<div class="card-image" style="background-image: url(\'' . $image_url . '\');"></div>';
                    echo '<div class="card-content">';
                    echo '<h3 class="card-title">' . $neighborhood->name . '</h3>';
                    echo '<p class="card-description">' . $neighborhood->description . '</p>';
                    echo '<a href="' . get_term_link($neighborhood) . '" class="card-link">Explore →</a>';
                    echo '</div></div>';
                }
            }
            ?>
        </div>
    </div>
</section>

<!-- Featured Rooftops Section -->
<section id="featured-rooftops" class="section">
    <div class="container">
        <h2 class="section-title">Featured Rooftops in Barcelona</h2>
        <p class="section-subtitle">Discover our handpicked selection of the best rooftops Barcelona has to offer</p>
        
        <div class="cards-grid">
            <?php
            // Query for rooftops
            $rooftops_query = new WP_Query(array(
                'post_type' => 'rooftops',
                'posts_per_page' => 9,
                'meta_query' => array(
                    array(
                        'key' => '_featured_rooftop',
                        'value' => 'yes',
                        'compare' => '='
                    )
                )
            ));
            
            // If no rooftops exist, create sample data
            if (!$rooftops_query->have_posts()) {
                $sample_rooftops = array(
                    array('name' => 'Sky Bar Barcelona', 'address' => 'Avinguda Diagonal, 477, Eixample', 'image' => 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.8),
                    array('name' => 'Terrat Restaurant', 'address' => 'Carrer de Mallorca, 259, Eixample', 'image' => 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.6),
                    array('name' => 'La Isabela', 'address' => 'Hotel 1898, La Rambla, 109, Ciutat Vella', 'image' => 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.7),
                    array('name' => 'Mirablau', 'address' => 'Plaça del Doctor Andreu, s/n, Sarrià-Sant Gervasi', 'image' => 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.5),
                    array('name' => 'Eclipse Bar', 'address' => 'Moll de Barcelona, s/n, Ciutat Vella', 'image' => 'https://images.unsplash.com/photo-1572116469696-31de0f17cc34?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.9),
                    array('name' => 'Rooftop Nine', 'address' => 'Carrer de Rosselló, 265, Eixample', 'image' => 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.4),
                    array('name' => 'Aire Rooftop', 'address' => 'Carrer de Pau Claris, 122, Eixample', 'image' => 'https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.6),
                    array('name' => 'Ohla Boutique Hotel', 'address' => 'Via Laietana, 49, Ciutat Vella', 'image' => 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.8),
                    array('name' => 'Hotel Barcelona Center', 'address' => 'Carrer de Balmes, 103, Eixample', 'image' => 'https://images.unsplash.com/photo-1506521781263-d8422e82f27a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80', 'rating' => 4.3)
                );
                
                foreach ($sample_rooftops as $rooftop) {
                    echo '<div class="rooftop-card" onclick="window.location.href=\'/rooftop/' . sanitize_title($rooftop['name']) . '/\'">';
                    echo '<div class="rooftop-image" style="background-image: url(\'' . $rooftop['image'] . '\');"></div>';
                    echo '<div class="rooftop-info">';
                    echo '<h3 class="rooftop-name">' . $rooftop['name'] . '</h3>';
                    echo '<p class="rooftop-address">' . $rooftop['address'] . '</p>';
                    echo '<div class="rooftop-rating">';
                    echo '<span class="stars">';
                    for ($i = 1; $i <= 5; $i++) {
                        if ($i <= floor($rooftop['rating'])) {
                            echo '★';
                        } elseif ($i - 0.5 <= $rooftop['rating']) {
                            echo '☆';
                        } else {
                            echo '☆';
                        }
                    }
                    echo '</span>';
                    echo '<span>' . $rooftop['rating'] . '</span>';
                    echo '</div>';
                    echo '</div></div>';
                }
            } else {
                while ($rooftops_query->have_posts()) {
                    $rooftops_query->the_post();
                    $address = get_post_meta(get_the_ID(), '_rooftop_address', true);
                    $rating = get_post_meta(get_the_ID(), '_rooftop_rating', true);
                    
                    echo '<div class="rooftop-card" onclick="window.location.href=\'' . get_permalink() . '\'">';
                    if (has_post_thumbnail()) {
                        echo '<div class="rooftop-image" style="background-image: url(\'' . get_the_post_thumbnail_url(get_the_ID(), 'medium') . '\');"></div>';
                    } else {
                        echo '<div class="rooftop-image" style="background-image: url(\'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80\');"></div>';
                    }
                    echo '<div class="rooftop-info">';
                    echo '<h3 class="rooftop-name">' . get_the_title() . '</h3>';
                    echo '<p class="rooftop-address">' . esc_html($address) . '</p>';
                    if ($rating) {
                        echo '<div class="rooftop-rating">';
                        echo '<span class="stars">';
                        for ($i = 1; $i <= 5; $i++) {
                            if ($i <= floor($rating)) {
                                echo '★';
                            } elseif ($i - 0.5 <= $rating) {
                                echo '☆';
                            } else {
                                echo '☆';
                            }
                        }
                        echo '</span>';
                        echo '<span>' . $rating . '</span>';
                        echo '</div>';
                    }
                    echo '</div></div>';
                }
                wp_reset_postdata();
            }
            ?>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="cta-section">
    <div class="container">
        <h2 class="cta-title">Ready to Explore Barcelona's Rooftops?</h2>
        <p class="cta-description">Join thousands of visitors who have discovered the best rooftop experiences in Barcelona. Start your journey today!</p>
        <a href="/rooftops/" class="cta-btn">Browse All Rooftops</a>
        <a href="/contact/" class="cta-btn secondary">Contact Us</a>
    </div>
</section>

<?php get_footer(); ?>
